{"version": 3, "$schema": "https://schemas.wp.org/wp/6.7/theme.json", "title": "Roboto Slab & Manrope", "slug": "typography-preset-4", "settings": {"typography": {"fontFamilies": [{"name": "<PERSON><PERSON> Slab", "slug": "roboto-slab", "fontFamily": "\"Roboto Slab\", serif", "fontFace": [{"fontFamily": "\"Roboto Slab\"", "fontStyle": "normal", "fontWeight": "100 900", "src": ["file:./assets/fonts/roboto-slab/RobotoSlab-VariableFont_wght.woff2"]}]}, {"name": "Manrope", "slug": "manrope", "fontFamily": "Man<PERSON><PERSON>, sans-serif", "fontFace": [{"src": ["file:./assets/fonts/manrope/Manrope-VariableFont_wght.woff2"], "fontWeight": "200 800", "fontStyle": "normal", "fontFamily": "Manrope"}]}], "fontSizes": [{"fluid": false, "name": "Small", "size": "0.875rem", "slug": "small"}, {"fluid": {"max": "1.125rem", "min": "1rem"}, "name": "Medium", "size": "1rem", "slug": "medium"}, {"fluid": {"max": "1.375rem", "min": "1.125rem"}, "name": "Large", "size": "1.38rem", "slug": "large"}, {"fluid": {"max": "2rem", "min": "1.75rem"}, "name": "Extra Large", "size": "1.75rem", "slug": "x-large"}, {"fluid": {"max": "2.4rem", "min": "2.15rem"}, "name": "Extra Extra Large", "size": "2.15rem", "slug": "xx-large"}]}}, "styles": {"typography": {"letterSpacing": "0"}, "blocks": {"core/navigation": {"typography": {"fontSize": "var:preset|font-size|large", "letterSpacing": "-0.28px", "textTransform": "uppercase"}}, "core/post-author": {"typography": {"fontSize": "var:preset|font-size|small"}}, "core/post-author-name": {"typography": {"fontSize": "var:preset|font-size|small"}}, "core/post-terms": {"typography": {"fontWeight": "500"}}, "core/pullquote": {"typography": {"fontFamily": "var:preset|font-family|roboto-slab", "fontSize": "var:preset|font-size|xx-large", "fontWeight": "200"}}, "core/search": {"typography": {"textTransform": "uppercase"}}, "core/site-tagline": {"typography": {"fontSize": "var:preset|font-size|large"}}, "core/site-title": {"typography": {"textTransform": "uppercase"}}}, "elements": {"button": {"typography": {"fontWeight": "500", "letterSpacing": "-0.36px", "textTransform": "uppercase"}}, "heading": {"typography": {"fontFamily": "var:preset|font-family|roboto-slab", "fontWeight": "300", "letterSpacing": "-0.5px", "lineHeight": "1.2"}}}}}