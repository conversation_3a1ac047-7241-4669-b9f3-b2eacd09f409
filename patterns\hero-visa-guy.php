<?php
/**
 * Title: Visa Guy <PERSON> Section
 * Slug: visa-guy-theme/hero-visa-guy
 * Categories: banner, featured
 * Description: Professional hero section inspired by Visa Guy website with call-to-action buttons and service highlights
 */
?>

<!-- wp:group {"align":"full","style":{"spacing":{"padding":{"top":"var:preset|spacing|80","bottom":"var:preset|spacing|80","left":"var:preset|spacing|50","right":"var:preset|spacing|50"},"margin":{"top":"0","bottom":"0"}},"color":{"gradient":"linear-gradient(135deg,var(--wp--preset--color--accent-6) 0%,var(--wp--preset--color--base) 100%)"}},"className":"hero-section","layout":{"type":"constrained","wideSize":"1200px"}} -->
<div class="wp-block-group alignfull hero-section has-background" style="background:linear-gradient(135deg,var(--wp--preset--color--accent-6) 0%,var(--wp--preset--color--base) 100%);margin-top:0;margin-bottom:0;padding-top:var(--wp--preset--spacing--80);padding-right:var(--wp--preset--spacing--50);padding-bottom:var(--wp--preset--spacing--80);padding-left:var(--wp--preset--spacing--50)">

<!-- wp:columns {"align":"wide","style":{"spacing":{"blockGap":{"top":"var:preset|spacing|60","left":"var:preset|spacing|60"}}}} -->
<div class="wp-block-columns alignwide">

<!-- wp:column {"width":"60%","style":{"spacing":{"padding":{"right":"var:preset|spacing|40"}}}} -->
<div class="wp-block-column" style="padding-right:var(--wp--preset--spacing--40);flex-basis:60%">

<!-- wp:paragraph {"style":{"typography":{"textTransform":"uppercase","letterSpacing":"1px","fontWeight":"600"}},"textColor":"accent-1","fontSize":"small","className":"professional-subheading"} -->
<p class="professional-subheading has-accent-1-color has-text-color has-small-font-size" style="font-weight:600;letter-spacing:1px;text-transform:uppercase">Visa Services in Dubai UAE</p>
<!-- /wp:paragraph -->

<!-- wp:heading {"level":1,"style":{"typography":{"fontSize":"clamp(2.5rem, 5vw, 4rem)","lineHeight":"1.1","fontWeight":"700"}},"textColor":"contrast","className":"professional-heading"} -->
<h1 class="wp-block-heading professional-heading has-contrast-color has-text-color" style="font-size:clamp(2.5rem, 5vw, 4rem);font-weight:700;line-height:1.1">Visa Simplified.</h1>
<!-- /wp:heading -->

<!-- wp:paragraph {"style":{"typography":{"fontSize":"1.2rem","lineHeight":"1.6"}},"textColor":"accent-4"} -->
<p class="has-accent-4-color has-text-color" style="font-size:1.2rem;line-height:1.6">Navigating the visa application process can be complex and time-consuming. But don't worry, we're here to help! At The Visa Guy, we specialize in making visa applications simple and stress-free.</p>
<!-- /wp:paragraph -->

<!-- wp:buttons {"style":{"spacing":{"margin":{"top":"var:preset|spacing|50"},"blockGap":"var:preset|spacing|30"}}} -->
<div class="wp-block-buttons" style="margin-top:var(--wp--preset--spacing--50)">

<!-- wp:button {"backgroundColor":"accent-8","textColor":"base","style":{"border":{"radius":"25px"},"typography":{"fontWeight":"600","textTransform":"uppercase","letterSpacing":"0.5px"},"spacing":{"padding":{"left":"var:preset|spacing|60","right":"var:preset|spacing|60","top":"var:preset|spacing|40","bottom":"var:preset|spacing|40"}}},"className":"is-style-whatsapp"} -->
<div class="wp-block-button is-style-whatsapp"><a class="wp-block-button__link has-base-color has-accent-8-background-color has-text-color has-background wp-element-button" style="border-radius:25px;font-weight:600;letter-spacing:0.5px;padding-top:var(--wp--preset--spacing--40);padding-right:var(--wp--preset--spacing--60);padding-bottom:var(--wp--preset--spacing--40);padding-left:var(--wp--preset--spacing--60);text-transform:uppercase">📱 Book Free Consultation</a></div>
<!-- /wp:button -->

<!-- wp:button {"backgroundColor":"accent-7","textColor":"base","style":{"border":{"radius":"25px"},"typography":{"fontWeight":"600","textTransform":"uppercase","letterSpacing":"0.5px"},"spacing":{"padding":{"left":"var:preset|spacing|60","right":"var:preset|spacing|60","top":"var:preset|spacing|40","bottom":"var:preset|spacing|40"}}},"className":"is-style-call"} -->
<div class="wp-block-button is-style-call"><a class="wp-block-button__link has-base-color has-accent-7-background-color has-text-color has-background wp-element-button" style="border-radius:25px;font-weight:600;letter-spacing:0.5px;padding-top:var(--wp--preset--spacing--40);padding-right:var(--wp--preset--spacing--60);padding-bottom:var(--wp--preset--spacing--40);padding-left:var(--wp--preset--spacing--60);text-transform:uppercase">📞 Call Us Now</a></div>
<!-- /wp:button -->

</div>
<!-- /wp:buttons -->

</div>
<!-- /wp:column -->

<!-- wp:column {"width":"40%"} -->
<div class="wp-block-column" style="flex-basis:40%">

<!-- wp:image {"sizeSlug":"large","linkDestination":"none","style":{"border":{"radius":"15px"}}} -->
<figure class="wp-block-image size-large has-custom-border"><img src="https://images.unsplash.com/photo-1521791136064-7986c2920216?ixlib=rb-4.0.3&amp;ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&amp;auto=format&amp;fit=crop&amp;w=1000&amp;q=80" alt="Professional visa consultation services" style="border-radius:15px"/></figure>
<!-- /wp:image -->

</div>
<!-- /wp:column -->

</div>
<!-- /wp:columns -->

</div>
<!-- /wp:group -->
