/*
Theme Name: White Wings Professional Theme
Theme URI: https://whitewings.ae/
Author: White Wings Team
Author URI: https://whitewings.ae/
Description: A professional WordPress theme inspired by White Wings branding and Visa Guy UAE's design excellence. Features a clean, modern layout optimized for visa services, immigration consultancy, and professional service businesses. Includes specialized patterns for service listings, testimonials, contact forms, and call-to-action sections. The theme emphasizes trust, professionalism, and user-friendly navigation with White Wings' signature blue and white color scheme that conveys reliability and expertise in reaching new heights.
Requires at least: 6.7
Tested up to: 6.8
Requires PHP: 7.2
Version: 1.0
License: GNU General Public License v2 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html
Text Domain: white-wings-theme
Tags: business, professional, services, visa, immigration, consulting, blue, white, clean, modern, responsive, full-site-editing, block-patterns, custom-colors, custom-logo, featured-images, accessibility-ready, white-wings, reach-new-heights
*/

/*
 * Link styles
 * https://github.com/WordPress/gutenberg/issues/42319
 */
a {
	text-decoration-thickness: 1px !important;
	text-underline-offset: .1em;
}

/* Focus styles */
:where(.wp-site-blocks *:focus) {
	outline-width: 2px;
	outline-style: solid;
}

/* Increase the bottom margin on submenus, so that the outline is visible. */
.wp-block-navigation .wp-block-navigation-submenu .wp-block-navigation-item:not(:last-child) {
	margin-bottom: 3px;
}

/* Increase the outline offset on the parent menu items, so that the outline does not touch the text. */
.wp-block-navigation .wp-block-navigation-item .wp-block-navigation-item__content {
	outline-offset: 4px;
}

/* Remove outline offset from the submenus, otherwise the outline is visible outside the submenu container. */
.wp-block-navigation .wp-block-navigation-item ul.wp-block-navigation__submenu-container .wp-block-navigation-item__content {
	outline-offset: 0;
}

/*
 * Progressive enhancement to reduce widows and orphans
 * https://github.com/WordPress/gutenberg/issues/55190
 */
h1, h2, h3, h4, h5, h6, blockquote, caption, figcaption, p {
	text-wrap: pretty;
}

/*
 * Change the position of the more block on the front, by making it a block level element.
 * https://github.com/WordPress/gutenberg/issues/65934
*/
.more-link {
	display: block;
}

/* White Wings Professional Styles */

/* Professional Button Styles - White Wings Blue Theme */
.wp-block-button__link {
	border-radius: 25px !important;
	padding: 12px 30px !important;
	font-weight: 600 !important;
	text-transform: uppercase !important;
	letter-spacing: 0.5px !important;
	transition: all 0.3s ease !important;
}

.wp-block-button.is-style-fill .wp-block-button__link {
	background: linear-gradient(135deg, #00bfff 0%, #0080ff 100%) !important;
	color: white !important;
}

.wp-block-button.is-style-fill .wp-block-button__link:hover {
	background: linear-gradient(135deg, #0080ff 0%, #00bfff 100%) !important;
	transform: translateY(-2px) !important;
	box-shadow: 0 8px 25px rgba(0, 191, 255, 0.4) !important;
}

/* WhatsApp Style Button */
.wp-block-button.is-style-whatsapp .wp-block-button__link {
	background: #25d366 !important;
	color: white !important;
}

.wp-block-button.is-style-whatsapp .wp-block-button__link:hover {
	background: #128c7e !important;
	transform: translateY(-2px) !important;
	box-shadow: 0 8px 25px rgba(37, 211, 102, 0.3) !important;
}

/* Call Button Style */
.wp-block-button.is-style-call .wp-block-button__link {
	background: #ff6b35 !important;
	color: white !important;
}

.wp-block-button.is-style-call .wp-block-button__link:hover {
	background: #e55a2b !important;
	transform: translateY(-2px) !important;
	box-shadow: 0 8px 25px rgba(255, 107, 53, 0.3) !important;
}

/* White Wings Service Cards */
.visa-service-card {
	background: white;
	border-radius: 15px;
	padding: 30px;
	box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
	transition: all 0.3s ease;
	border: 1px solid #e6f7ff;
}

.visa-service-card:hover {
	transform: translateY(-5px);
	box-shadow: 0 15px 40px rgba(0, 191, 255, 0.2);
	border-color: #00bfff;
}

/* Professional Header Styles - White Wings */
.site-header {
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(10px);
	border-bottom: 1px solid #e6f7ff;
}

/* Hero Section - White Wings Blue Gradient */
.hero-section {
	background: linear-gradient(135deg, #e6f7ff 0%, #ffffff 100%);
	padding: 80px 0;
	position: relative;
}

.hero-section::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: radial-gradient(circle at 30% 50%, rgba(0, 191, 255, 0.1) 0%, transparent 50%);
	pointer-events: none;
}

/* Testimonial Styles - White Wings Theme */
.testimonial-card {
	background: #f8fdff;
	border-left: 4px solid #00bfff;
	padding: 25px;
	border-radius: 10px;
	margin: 20px 0;
	box-shadow: 0 3px 15px rgba(0, 191, 255, 0.1);
}

/* FAQ Styles - White Wings */
.faq-item {
	border-bottom: 1px solid #e6f7ff;
	padding: 20px 0;
}

.faq-question {
	font-weight: 600;
	color: #00bfff;
	margin-bottom: 10px;
}

/* Country Flag Icons */
.country-flag {
	width: 30px;
	height: 20px;
	border-radius: 3px;
	margin-right: 10px;
	display: inline-block;
	box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* Professional Typography - White Wings */
.professional-heading {
	color: #1a1a1a;
	font-weight: 700;
	line-height: 1.2;
}

.professional-subheading {
	color: #00bfff;
	font-weight: 600;
	text-transform: uppercase;
	letter-spacing: 1px;
	font-size: 14px;
	margin-bottom: 10px;
}

/* Contact Information Styles - White Wings Blue */
.contact-info {
	background: linear-gradient(135deg, #00bfff 0%, #0080ff 100%);
	color: white;
	padding: 15px 25px;
	border-radius: 10px;
	display: inline-block;
	margin: 10px;
	box-shadow: 0 4px 15px rgba(0, 191, 255, 0.3);
}

.contact-info a {
	color: white;
	text-decoration: none;
	font-weight: 600;
}

.contact-info:hover {
	background: linear-gradient(135deg, #0080ff 0%, #00bfff 100%);
	transform: translateY(-2px);
	box-shadow: 0 6px 20px rgba(0, 191, 255, 0.4);
}

/* White Wings Brand Colors */
:root {
	--white-wings-primary: #00bfff;
	--white-wings-secondary: #0080ff;
	--white-wings-light: #e6f7ff;
	--white-wings-dark: #0066cc;
	--white-wings-gradient: linear-gradient(135deg, #00bfff 0%, #0080ff 100%);
	--white-wings-shadow: rgba(0, 191, 255, 0.3);
}

/* Professional Navigation Styles */
.wp-block-navigation-link a {
	color: #1a1a1a !important;
	font-weight: 500 !important;
	transition: color 0.3s ease !important;
}

.wp-block-navigation-link a:hover {
	color: var(--white-wings-primary) !important;
}

/* Professional Card Hover Effects */
.wp-block-group:hover {
	transform: translateY(-3px);
	transition: transform 0.3s ease;
}

/* White Wings Logo Glow Effect */
.white-wings-logo {
	filter: drop-shadow(0 0 10px rgba(0, 191, 255, 0.3));
	transition: filter 0.3s ease;
}

.white-wings-logo:hover {
	filter: drop-shadow(0 0 20px rgba(0, 191, 255, 0.5));
}

/* Professional Section Dividers */
.section-divider {
	height: 2px;
	background: var(--white-wings-gradient);
	margin: 60px 0;
	border-radius: 1px;
}

/* Professional Animations and Effects - White Wings */

/* Smooth Page Transitions */
* {
	transition: all 0.3s ease;
}

/* Advanced Button Hover Effects */
.wp-block-button__link:hover {
	transform: translateY(-3px) scale(1.02);
	box-shadow: 0 10px 30px rgba(0, 191, 255, 0.4);
}

/* Card Hover Animations */
.visa-service-card {
	position: relative;
	overflow: hidden;
}

.visa-service-card::before {
	content: '';
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(90deg, transparent, rgba(0, 191, 255, 0.1), transparent);
	transition: left 0.6s ease;
}

.visa-service-card:hover::before {
	left: 100%;
}

/* Professional Image Hover Effects */
.wp-block-image img {
	transition: transform 0.4s ease, filter 0.4s ease;
}

.wp-block-image:hover img {
	transform: scale(1.05);
	filter: brightness(1.1);
}

/* Navigation Hover Effects */
.wp-block-navigation-link a::after {
	content: '';
	position: absolute;
	bottom: -5px;
	left: 0;
	width: 0;
	height: 2px;
	background: var(--white-wings-primary);
	transition: width 0.3s ease;
}

.wp-block-navigation-link a:hover::after {
	width: 100%;
}

/* Professional Loading Animation */
@keyframes fadeInUp {
	from {
		opacity: 0;
		transform: translateY(30px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

.wp-block-group {
	animation: fadeInUp 0.6s ease-out;
}

/* Testimonial Card Animations */
.testimonial-card {
	transition: all 0.4s ease;
	position: relative;
}

.testimonial-card:hover {
	transform: translateY(-8px) rotate(1deg);
	box-shadow: 0 20px 40px rgba(0, 191, 255, 0.2);
}

/* FAQ Item Hover Effects */
.faq-item {
	transition: all 0.3s ease;
	border-radius: 8px;
	padding: 20px;
	margin: 10px 0;
}

.faq-item:hover {
	background: rgba(0, 191, 255, 0.05);
	transform: translateX(10px);
	border-left: 4px solid var(--white-wings-primary);
}

/* Professional Pulse Animation for CTA */
@keyframes pulse {
	0% {
		box-shadow: 0 0 0 0 rgba(0, 191, 255, 0.7);
	}
	70% {
		box-shadow: 0 0 0 10px rgba(0, 191, 255, 0);
	}
	100% {
		box-shadow: 0 0 0 0 rgba(0, 191, 255, 0);
	}
}

.is-style-fill .wp-block-button__link {
	animation: pulse 2s infinite;
}

/* Social Media Icons Hover */
.wp-block-social-links .wp-social-link {
	transition: all 0.3s ease;
}

.wp-block-social-links .wp-social-link:hover {
	transform: translateY(-5px) scale(1.1);
	filter: brightness(1.2);
}

/* Professional Scroll Animations */
@keyframes slideInLeft {
	from {
		opacity: 0;
		transform: translateX(-50px);
	}
	to {
		opacity: 1;
		transform: translateX(0);
	}
}

@keyframes slideInRight {
	from {
		opacity: 0;
		transform: translateX(50px);
	}
	to {
		opacity: 1;
		transform: translateX(0);
	}
}

/* Country Flag Hover Effects */
.country-flag {
	transition: all 0.3s ease;
	cursor: pointer;
}

.country-flag:hover {
	transform: scale(1.2) rotate(5deg);
	filter: brightness(1.2);
}

/* Professional Form Field Effects */
.wp-block-group input,
.wp-block-group textarea,
.wp-block-group select {
	transition: all 0.3s ease;
	border: 2px solid transparent;
}

.wp-block-group input:focus,
.wp-block-group textarea:focus,
.wp-block-group select:focus {
	border-color: var(--white-wings-primary);
	box-shadow: 0 0 0 3px rgba(0, 191, 255, 0.1);
	transform: translateY(-2px);
}

/* Comprehensive Responsive Design - White Wings */

/* Large Desktop (1200px and up) */
@media (min-width: 1200px) {
	.hero-section {
		padding: 100px 0;
	}

	.professional-heading {
		font-size: clamp(2.5rem, 5vw, 4.5rem);
	}

	.visa-service-card {
		padding: 40px;
	}
}

/* Desktop (992px to 1199px) */
@media (min-width: 992px) and (max-width: 1199px) {
	.wp-block-columns {
		gap: 30px;
	}

	.hero-section {
		padding: 80px 0;
	}
}

/* Tablet (768px to 991px) */
@media (min-width: 768px) and (max-width: 991px) {
	.hero-section {
		padding: 60px 0;
	}

	.professional-heading {
		font-size: clamp(2rem, 4vw, 3rem);
	}

	.wp-block-columns {
		flex-direction: column;
	}

	.visa-service-card {
		padding: 25px;
		margin-bottom: 20px;
	}

	.wp-block-button__link {
		padding: 12px 25px !important;
		font-size: 0.95rem !important;
	}

	/* Navigation adjustments for tablet */
	.wp-block-navigation {
		flex-direction: column;
		gap: 15px;
	}

	/* Contact form adjustments */
	.contact-form .wp-block-columns {
		flex-direction: column;
	}
}

/* Mobile Large (576px to 767px) */
@media (min-width: 576px) and (max-width: 767px) {
	.hero-section {
		padding: 50px 0;
	}

	.professional-heading {
		font-size: clamp(1.8rem, 4vw, 2.5rem);
		text-align: center;
	}

	.wp-block-button__link {
		padding: 12px 20px !important;
		font-size: 0.9rem !important;
		width: 100%;
		text-align: center;
	}

	.visa-service-card {
		padding: 20px;
		margin-bottom: 15px;
	}

	/* Stack columns vertically */
	.wp-block-columns {
		flex-direction: column;
		gap: 20px;
	}

	/* Adjust testimonials for mobile */
	.testimonial-card {
		padding: 20px;
		margin: 15px 0;
	}

	/* FAQ adjustments */
	.faq-item {
		padding: 15px;
	}

	.faq-question {
		font-size: 1.1rem;
	}
}

/* Mobile Small (up to 575px) */
@media (max-width: 575px) {
	.hero-section {
		padding: 40px 0;
	}

	.professional-heading {
		font-size: clamp(1.5rem, 4vw, 2rem);
		text-align: center;
		line-height: 1.3;
	}

	.professional-subheading {
		font-size: 0.8rem;
		text-align: center;
	}

	.wp-block-button__link {
		padding: 10px 15px !important;
		font-size: 0.85rem !important;
		width: 100%;
		text-align: center;
		margin-bottom: 10px;
	}

	.visa-service-card {
		padding: 15px;
		margin-bottom: 15px;
	}

	.contact-info {
		margin: 5px 0;
		padding: 10px 15px;
		width: 100%;
		text-align: center;
	}

	/* Navigation mobile menu */
	.wp-block-navigation {
		flex-direction: column;
		gap: 10px;
		width: 100%;
	}

	.wp-block-navigation-link {
		width: 100%;
		text-align: center;
	}

	/* Header adjustments */
	.site-header {
		padding: 15px 20px;
	}

	.white-wings-logo {
		width: 60px;
	}

	/* Footer adjustments */
	.wp-block-social-links {
		justify-content: center;
		flex-wrap: wrap;
	}

	/* Form adjustments */
	.contact-form input,
	.contact-form textarea,
	.contact-form select {
		width: 100%;
		margin-bottom: 15px;
	}

	/* Country grid adjustments */
	.wp-block-gallery {
		columns: 2;
	}

	/* Testimonial adjustments */
	.testimonial-card {
		padding: 15px;
		margin: 10px 0;
	}

	/* FAQ adjustments */
	.faq-item {
		padding: 12px;
	}

	.faq-question {
		font-size: 1rem;
	}

	/* Disable complex animations on mobile for performance */
	.visa-service-card::before,
	.is-style-fill .wp-block-button__link {
		animation: none;
	}

	.visa-service-card:hover,
	.testimonial-card:hover,
	.faq-item:hover {
		transform: none;
	}

	/* Ensure touch-friendly spacing */
	.wp-block-button {
		margin: 10px 0;
	}

	/* Media section adjustments */
	.media-logos {
		flex-direction: column;
		gap: 15px;
	}

	/* Country carousel adjustments */
	.country-carousel {
		overflow-x: auto;
		scroll-snap-type: x mandatory;
	}

	.country-carousel .wp-block-image {
		scroll-snap-align: center;
		min-width: 200px;
	}
}

/* Extra Small Mobile (up to 375px) */
@media (max-width: 375px) {
	.hero-section {
		padding: 30px 0;
	}

	.professional-heading {
		font-size: 1.3rem;
		line-height: 1.2;
	}

	.wp-block-button__link {
		padding: 8px 12px !important;
		font-size: 0.8rem !important;
	}

	.visa-service-card {
		padding: 12px;
	}

	.site-header {
		padding: 10px 15px;
	}

	.white-wings-logo {
		width: 50px;
	}

	/* Ultra-compact layout */
	.wp-block-columns {
		gap: 10px;
	}

	.testimonial-card,
	.faq-item {
		padding: 10px;
	}
}

/* Print Styles */
@media print {
	.wp-block-button,
	.wp-block-social-links,
	.contact-form {
		display: none;
	}

	.professional-heading {
		color: black !important;
	}

	.visa-service-card {
		border: 1px solid #ccc;
		break-inside: avoid;
	}
}

/* High DPI / Retina Display Adjustments */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
	.white-wings-logo,
	.country-flag,
	.wp-block-image img {
		image-rendering: -webkit-optimize-contrast;
		image-rendering: crisp-edges;
	}
}

/* Landscape Mobile Orientation */
@media (max-width: 767px) and (orientation: landscape) {
	.hero-section {
		padding: 30px 0;
	}

	.professional-heading {
		font-size: 1.8rem;
	}

	/* Adjust navigation for landscape */
	.wp-block-navigation {
		flex-direction: row;
		flex-wrap: wrap;
		justify-content: center;
	}
}
