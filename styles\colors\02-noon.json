{"$schema": "https://schemas.wp.org/wp/6.7/theme.json", "version": 3, "title": "<PERSON>on", "settings": {"color": {"palette": [{"color": "#F8F7F5", "name": "Base", "slug": "base"}, {"color": "#191919", "name": "Contrast", "slug": "contrast"}, {"color": "#FFFFFF", "name": "Accent 1", "slug": "accent-1"}, {"color": "#F5B684", "name": "Accent 2", "slug": "accent-2"}, {"color": "#191919", "name": "Accent 3", "slug": "accent-3"}, {"color": "#5F5F5F", "name": "Accent 4", "slug": "accent-4"}, {"color": "#F1EEE9", "name": "Accent 5", "slug": "accent-5"}, {"color": "#19191933", "name": "Accent 6", "slug": "accent-6"}]}}, "styles": {"color": {"text": "var:preset|color|accent-4"}, "blocks": {"core/button": {"border": {"color": "var:preset|color|contrast"}}, "core/post-title": {"color": {"text": "var:preset|color|accent-3"}, "elements": {"link": {"color": {"text": "currentColor"}}}}, "core/pullquote": {"color": {"text": "var:preset|color|accent-3"}, "elements": {"link": {"color": {"text": "currentColor"}}}}, "core/query-title": {"color": {"text": "var:preset|color|accent-3"}, "elements": {"link": {"color": {"text": "currentColor"}}}}}, "elements": {"button": {"color": {"background": "var:preset|color|contrast", "text": "var:preset|color|base"}}, "heading": {"color": {"text": "var:preset|color|accent-3"}}, "link": {"color": {"text": "currentColor"}}}, "variations": {"section-4": {"color": {"text": "var:preset|color|accent-2"}, "elements": {"button": {":hover": {"color": {"background": "color-mix(in srgb, var(--wp--preset--color--accent-2) 85%, transparent)"}}}, "heading": {"color": {"text": "currentColor"}}, "link": {"color": {"text": "currentColor"}}}}, "section-5": {"color": {"text": "var:preset|color|base"}, "elements": {"button": {":hover": {"color": {"background": "color-mix(in srgb, var(--wp--preset--color--base) 85%, transparent)"}}}, "heading": {"color": {"text": "var:preset|color|base"}}, "link": {"color": {"text": "currentColor"}}}}}}}