{"$schema": "https://schemas.wp.org/wp/6.7/theme.json", "version": 3, "title": "Sunrise", "settings": {"color": {"palette": [{"color": "#330616", "name": "Base", "slug": "base"}, {"color": "#FFFFFF", "name": "Contrast", "slug": "contrast"}, {"color": "#F0FDA6", "name": "Accent 1", "slug": "accent-1"}, {"color": "#DB9AB1", "name": "Accent 2", "slug": "accent-2"}, {"color": "#C1E4E7", "name": "Accent 3", "slug": "accent-3"}, {"color": "#DB9AB1", "name": "Accent 4", "slug": "accent-4"}, {"color": "#4A1628", "name": "Accent 5", "slug": "accent-5"}, {"color": "#DB9AB133", "name": "Accent 6", "slug": "accent-6"}]}, "typography": {"fontFamilies": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "slug": "platypi", "fontFamily": "<PERSON><PERSON><PERSON><PERSON>", "fontFace": [{"fontFamily": "<PERSON><PERSON><PERSON><PERSON>", "fontStyle": "normal", "fontWeight": "300 800", "src": ["file:./assets/fonts/platypi/Platypi-VariableFont_wght.woff2"]}, {"fontFamily": "<PERSON><PERSON><PERSON><PERSON>", "fontStyle": "italic", "fontWeight": "300 800", "src": ["file:./assets/fonts/platypi/Platypi-Italic-VariableFont_wght.woff2"]}]}, {"name": "Literata", "slug": "literata", "fontFamily": "Literata, serif", "fontFace": [{"src": ["file:./assets/fonts/literata/Literata72pt-ExtraLight.woff2"], "fontWeight": "200", "fontStyle": "normal", "fontFamily": "Literata"}, {"src": ["file:./assets/fonts/literata/Literata72pt-ExtraLightItalic.woff2"], "fontWeight": "200", "fontStyle": "italic", "fontFamily": "Literata"}, {"src": ["file:./assets/fonts/literata/Literata72pt-Light.woff2"], "fontWeight": "300", "fontStyle": "normal", "fontFamily": "Literata"}, {"src": ["file:./assets/fonts/literata/Literata72pt-LightItalic.woff2"], "fontWeight": "300", "fontStyle": "italic", "fontFamily": "Literata"}, {"src": ["file:./assets/fonts/literata/Literata72pt-Regular.woff2"], "fontWeight": "400", "fontStyle": "normal", "fontFamily": "Literata"}, {"src": ["file:./assets/fonts/literata/Literata72pt-RegularItalic.woff2"], "fontWeight": "400", "fontStyle": "italic", "fontFamily": "Literata"}, {"src": ["file:./assets/fonts/literata/Literata72pt-Medium.woff2"], "fontWeight": "500", "fontStyle": "normal", "fontFamily": "Literata"}, {"src": ["file:./assets/fonts/literata/Literata72pt-MediumItalic.woff2"], "fontWeight": "500", "fontStyle": "italic", "fontFamily": "Literata"}, {"src": ["file:./assets/fonts/literata/Literata72pt-SemiBold.woff2"], "fontWeight": "600", "fontStyle": "normal", "fontFamily": "Literata"}, {"src": ["file:./assets/fonts/literata/Literata72pt-SemiBoldItalic.woff2"], "fontWeight": "600", "fontStyle": "italic", "fontFamily": "Literata"}, {"src": ["file:./assets/fonts/literata/Literata72pt-Bold.woff2"], "fontWeight": "700", "fontStyle": "normal", "fontFamily": "Literata"}, {"src": ["file:./assets/fonts/literata/Literata72pt-BoldItalic.woff2"], "fontWeight": "700", "fontStyle": "italic", "fontFamily": "Literata"}, {"src": ["file:./assets/fonts/literata/Literata72pt-ExtraBold.woff2"], "fontWeight": "800", "fontStyle": "normal", "fontFamily": "Literata"}, {"src": ["file:./assets/fonts/literata/Literata72pt-ExtraBoldItalic.woff2"], "fontWeight": "800", "fontStyle": "italic", "fontFamily": "Literata"}, {"src": ["file:./assets/fonts/literata/Literata72pt-Black.woff2"], "fontWeight": "900", "fontStyle": "normal", "fontFamily": "Literata"}, {"src": ["file:./assets/fonts/literata/Literata72pt-BlackItalic.woff2"], "fontWeight": "900", "fontStyle": "italic", "fontFamily": "Literata"}]}]}}, "styles": {"color": {"text": "var:preset|color|accent-2"}, "typography": {"fontFamily": "var:preset|font-family|literata", "fontSize": "1.5rem", "letterSpacing": "-0.24px", "lineHeight": "1.3"}, "blocks": {"core/code": {"color": {"text": "var:preset|color|accent-2", "background": "var:preset|color|accent-5"}}, "core/navigation": {"typography": {"fontSize": "1.25rem"}}, "core/post-author-name": {"color": {"text": "var:preset|color|contrast"}, "elements": {"link": {"color": {"text": "var:preset|color|contrast"}}}}, "core/post-terms": {"typography": {"fontWeight": "400"}, "color": {"text": "var:preset|color|contrast"}, "elements": {"link": {"color": {"text": "var:preset|color|contrast"}}}}, "core/post-title": {"typography": {"fontWeight": "800", "letterSpacing": "-0.96px"}, "elements": {"link": {"color": {"text": "var:preset|color|accent-2"}}}}, "core/pullquote": {"color": {"text": "var:preset|color|accent-2"}, "typography": {"fontFamily": "var:preset|font-family|platypi", "fontSize": "var:preset|font-size|x-large", "letterSpacing": "-0.76px", "fontWeight": "800"}, "elements": {"cite": {"typography": {"fontFamily": "var:preset|font-family|literata", "fontWeight": "400", "letterSpacing": "-0.14px"}, "color": {"text": "var:preset|color|accent-2"}}}}, "core/quote": {"color": {"text": "var:preset|color|accent-2"}, "typography": {"fontSize": "1.5rem", "fontWeight": "600", "letterSpacing": "-0.24px"}, "elements": {"cite": {"typography": {"letterSpacing": "-0.14px"}, "color": {"text": "var:preset|color|accent-2"}}}}, "core/site-title": {"typography": {"fontFamily": "var:preset|font-family|platypi", "fontSize": "30px", "fontWeight": "800", "letterSpacing": "-0.6px"}, "elements": {"link": {"color": {"text": "var:preset|color|accent-2"}}}}}, "elements": {"button": {"color": {"text": "var:preset|color|base", "background": "var:preset|color|accent-2"}, "border": {"radius": "0px"}, ":hover": {"color": {"background": "color-mix(in srgb, var(--wp--preset--color--accent-2) 85%, transparent)"}}, "typography": {"fontFamily": "var:preset|font-family|platypi", "fontSize": "1.5rem", "fontWeight": "800"}}, "heading": {"typography": {"fontFamily": "var:preset|font-family|platypi", "fontWeight": "800"}}, "link": {"color": {"text": "var:preset|color|contrast"}}}, "variations": {"post-terms-1": {"typography": {"fontSize": "16px"}, "elements": {"link": {"color": {"background": "var:preset|color|accent-5"}, "border": {"radius": "100px", "color": "var:preset|color|accent-5"}}}}, "section-1": {"color": {"text": "var:preset|color|accent-5", "background": "var:preset|color|contrast"}, "elements": {"link": {"color": {"text": "currentColor"}}, "button": {"color": {"background": "var:preset|color|base", "text": "var:preset|color|contrast"}, ":hover": {"color": {"background": "color-mix(in srgb, var(--wp--preset--color--base) 85%, transparent)"}}}}}, "section-2": {"color": {"text": "var:preset|color|base"}, "elements": {"link": {"color": {"text": "currentColor"}}, "button": {"color": {"background": "var:preset|color|base", "text": "var:preset|color|contrast"}, ":hover": {"color": {"background": "color-mix(in srgb, var(--wp--preset--color--base) 85%, transparent)"}}}}}, "section-3": {"color": {"text": "var:preset|color|base"}, "elements": {"link": {"color": {"text": "currentColor"}}, "button": {"color": {"background": "var:preset|color|base", "text": "var:preset|color|contrast"}, ":hover": {"color": {"background": "color-mix(in srgb, var(--wp--preset--color--base) 85%, transparent)"}}}}}, "section-4": {"color": {"text": "var:preset|color|base"}, "elements": {"link": {"color": {"text": "currentColor"}}, "button": {"color": {"background": "var:preset|color|base", "text": "var:preset|color|contrast"}, ":hover": {"color": {"background": "color-mix(in srgb, var(--wp--preset--color--base) 85%, transparent)"}}}}}, "section-5": {"color": {"text": "var:preset|color|contrast", "background": "var:preset|color|accent-5"}, "elements": {"heading": {"color": {"text": "currentColor"}}, "link": {"color": {"text": "currentColor"}}, "button": {"color": {"background": "var:preset|color|contrast", "text": "var:preset|color|base"}, ":hover": {"color": {"background": "color-mix(in srgb, var(--wp--preset--color--contrast) 85%, transparent)", "text": "var:preset|color|base"}}}}}}}}