<?php
/**
 * Title: White Wings Contact Form
 * Slug: white-wings-theme/contact-form-white-wings
 * Categories: contact, call-to-action
 * Description: Comprehensive contact form exactly like original website with all fields and social media links
 */
?>

<!-- wp:group {"align":"full","style":{"spacing":{"padding":{"top":"var:preset|spacing|80","bottom":"var:preset|spacing|80","left":"var:preset|spacing|50","right":"var:preset|spacing|50"},"margin":{"top":"0","bottom":"0"}},"color":{"background":"#f8fdff"}},"layout":{"type":"constrained","wideSize":"1200px"}} -->
<div class="wp-block-group alignfull has-background" style="background:#f8fdff;margin-top:0;margin-bottom:0;padding-top:var(--wp--preset--spacing--80);padding-right:var(--wp--preset--spacing--50);padding-bottom:var(--wp--preset--spacing--80);padding-left:var(--wp--preset--spacing--50)">

<!-- wp:columns {"align":"wide","style":{"spacing":{"blockGap":{"top":"var:preset|spacing|60","left":"var:preset|spacing|60"}}}} -->
<div class="wp-block-columns alignwide">

<!-- wp:column {"width":"50%"} -->
<div class="wp-block-column" style="flex-basis:50%">

<!-- wp:heading {"style":{"typography":{"fontSize":"2rem","fontWeight":"700","lineHeight":"1.2"}},"textColor":"contrast","className":"professional-heading"} -->
<h2 class="wp-block-heading professional-heading has-contrast-color has-text-color" style="font-size:2rem;font-weight:700;line-height:1.2">Want to know more?</h2>
<!-- /wp:heading -->

<!-- wp:paragraph {"style":{"typography":{"fontSize":"1.1rem","lineHeight":"1.6","fontWeight":"600"}},"textColor":"contrast"} -->
<p class="has-contrast-color has-text-color" style="font-size:1.1rem;font-weight:600;line-height:1.6">Fill out this form. We will connect you back soon!</p>
<!-- /wp:paragraph -->

<!-- wp:paragraph {"style":{"typography":{"fontSize":"1.1rem","lineHeight":"1.6","fontWeight":"600"}},"textColor":"accent-1"} -->
<p class="has-accent-1-color has-text-color" style="font-size:1.1rem;font-weight:600;line-height:1.6">Or connect us through any of our social media channel!</p>
<!-- /wp:paragraph -->

<!-- wp:group {"style":{"spacing":{"blockGap":"var:preset|spacing|20","margin":{"top":"var:preset|spacing|40"}}},"layout":{"type":"flex","flexWrap":"wrap"}} -->
<div class="wp-block-group" style="margin-top:var(--wp--preset--spacing--40)">

<!-- wp:button {"backgroundColor":"accent-8","textColor":"base","style":{"border":{"radius":"10px"},"typography":{"fontWeight":"600","fontSize":"0.9rem"},"spacing":{"padding":{"left":"var:preset|spacing|20","right":"var:preset|spacing|20","top":"var:preset|spacing|10","bottom":"var:preset|spacing|10"}}},"className":"is-style-whatsapp"} -->
<div class="wp-block-button is-style-whatsapp"><a class="wp-block-button__link has-base-color has-accent-8-background-color has-text-color has-background wp-element-button" href="https://bit.ly/hellovisaguyae" style="border-radius:10px;font-size:0.9rem;font-weight:600;padding-top:var(--wp--preset--spacing--10);padding-right:var(--wp--preset--spacing--20);padding-bottom:var(--wp--preset--spacing--10);padding-left:var(--wp--preset--spacing--20)">Whatsapp</a></div>
<!-- /wp:button -->

<!-- wp:button {"backgroundColor":"accent-5","textColor":"base","style":{"border":{"radius":"10px"},"typography":{"fontWeight":"600","fontSize":"0.9rem"},"spacing":{"padding":{"left":"var:preset|spacing|20","right":"var:preset|spacing|20","top":"var:preset|spacing|10","bottom":"var:preset|spacing|10"}}}} -->
<div class="wp-block-button"><a class="wp-block-button__link has-base-color has-accent-5-background-color has-text-color has-background wp-element-button" href="https://www.instagram.com/whitewings/" style="border-radius:10px;font-size:0.9rem;font-weight:600;padding-top:var(--wp--preset--spacing--10);padding-right:var(--wp--preset--spacing--20);padding-bottom:var(--wp--preset--spacing--10);padding-left:var(--wp--preset--spacing--20)">Instagram</a></div>
<!-- /wp:button -->

<!-- wp:button {"backgroundColor":"accent-1","textColor":"base","style":{"border":{"radius":"10px"},"typography":{"fontWeight":"600","fontSize":"0.9rem"},"spacing":{"padding":{"left":"var:preset|spacing|20","right":"var:preset|spacing|20","top":"var:preset|spacing|10","bottom":"var:preset|spacing|10"}}}} -->
<div class="wp-block-button"><a class="wp-block-button__link has-base-color has-accent-1-background-color has-text-color has-background wp-element-button" href="https://www.facebook.com/whitewings/" style="border-radius:10px;font-size:0.9rem;font-weight:600;padding-top:var(--wp--preset--spacing--10);padding-right:var(--wp--preset--spacing--20);padding-bottom:var(--wp--preset--spacing--10);padding-left:var(--wp--preset--spacing--20)">Facebook</a></div>
<!-- /wp:button -->

<!-- wp:button {"backgroundColor":"accent-2","textColor":"base","style":{"border":{"radius":"10px"},"typography":{"fontWeight":"600","fontSize":"0.9rem"},"spacing":{"padding":{"left":"var:preset|spacing|20","right":"var:preset|spacing|20","top":"var:preset|spacing|10","bottom":"var:preset|spacing|10"}}}} -->
<div class="wp-block-button"><a class="wp-block-button__link has-base-color has-accent-2-background-color has-text-color has-background wp-element-button" href="https://www.linkedin.com/company/white-wings/" style="border-radius:10px;font-size:0.9rem;font-weight:600;padding-top:var(--wp--preset--spacing--10);padding-right:var(--wp--preset--spacing--20);padding-bottom:var(--wp--preset--spacing--10);padding-left:var(--wp--preset--spacing--20)">Linkedin</a></div>
<!-- /wp:button -->

<!-- wp:button {"backgroundColor":"accent-3","textColor":"base","style":{"border":{"radius":"10px"},"typography":{"fontWeight":"600","fontSize":"0.9rem"},"spacing":{"padding":{"left":"var:preset|spacing|20","right":"var:preset|spacing|20","top":"var:preset|spacing|10","bottom":"var:preset|spacing|10"}}}} -->
<div class="wp-block-button"><a class="wp-block-button__link has-base-color has-accent-3-background-color has-text-color has-background wp-element-button" href="https://twitter.com/WhiteWings_" style="border-radius:10px;font-size:0.9rem;font-weight:600;padding-top:var(--wp--preset--spacing--10);padding-right:var(--wp--preset--spacing--20);padding-bottom:var(--wp--preset--spacing--10);padding-left:var(--wp--preset--spacing--20)">Twitter</a></div>
<!-- /wp:button -->

<!-- wp:button {"backgroundColor":"accent-4","textColor":"base","style":{"border":{"radius":"10px"},"typography":{"fontWeight":"600","fontSize":"0.9rem"},"spacing":{"padding":{"left":"var:preset|spacing|20","right":"var:preset|spacing|20","top":"var:preset|spacing|10","bottom":"var:preset|spacing|10"}}}} -->
<div class="wp-block-button"><a class="wp-block-button__link has-base-color has-accent-4-background-color has-text-color has-background wp-element-button" href="https://www.snapchat.com/add/whitewings.ae" style="border-radius:10px;font-size:0.9rem;font-weight:600;padding-top:var(--wp--preset--spacing--10);padding-right:var(--wp--preset--spacing--20);padding-bottom:var(--wp--preset--spacing--10);padding-left:var(--wp--preset--spacing--20)">Snapchat</a></div>
<!-- /wp:button -->

<!-- wp:button {"backgroundColor":"accent-7","textColor":"base","style":{"border":{"radius":"10px"},"typography":{"fontWeight":"600","fontSize":"0.9rem"},"spacing":{"padding":{"left":"var:preset|spacing|20","right":"var:preset|spacing|20","top":"var:preset|spacing|10","bottom":"var:preset|spacing|10"}}}} -->
<div class="wp-block-button"><a class="wp-block-button__link has-base-color has-accent-7-background-color has-text-color has-background wp-element-button" href="https://www.youtube.com/@whitewings" style="border-radius:10px;font-size:0.9rem;font-weight:600;padding-top:var(--wp--preset--spacing--10);padding-right:var(--wp--preset--spacing--20);padding-bottom:var(--wp--preset--spacing--10);padding-left:var(--wp--preset--spacing--20)">Youtube</a></div>
<!-- /wp:button -->

</div>
<!-- /wp:group -->

<!-- wp:group {"style":{"spacing":{"blockGap":"var:preset|spacing|20","margin":{"top":"var:preset|spacing|40"}}},"layout":{"type":"flex","flexWrap":"wrap"}} -->
<div class="wp-block-group" style="margin-top:var(--wp--preset--spacing--40)">

<!-- wp:button {"backgroundColor":"accent-8","textColor":"base","style":{"border":{"radius":"15px"},"typography":{"fontWeight":"600","fontSize":"1rem"},"spacing":{"padding":{"left":"var:preset|spacing|30","right":"var:preset|spacing|30","top":"var:preset|spacing|20","bottom":"var:preset|spacing|20"}}},"className":"is-style-whatsapp"} -->
<div class="wp-block-button is-style-whatsapp"><a class="wp-block-button__link has-base-color has-accent-8-background-color has-text-color has-background wp-element-button" href="https://wa.link/ikcm1z" style="border-radius:15px;font-size:1rem;font-weight:600;padding-top:var(--wp--preset--spacing--20);padding-right:var(--wp--preset--spacing--30);padding-bottom:var(--wp--preset--spacing--20);padding-left:var(--wp--preset--spacing--30)">💬 WhatsApp</a></div>
<!-- /wp:button -->

<!-- wp:button {"backgroundColor":"accent-7","textColor":"base","style":{"border":{"radius":"15px"},"typography":{"fontWeight":"600","fontSize":"1rem"},"spacing":{"padding":{"left":"var:preset|spacing|30","right":"var:preset|spacing|30","top":"var:preset|spacing|20","bottom":"var:preset|spacing|20"}}},"className":"is-style-call"} -->
<div class="wp-block-button is-style-call"><a class="wp-block-button__link has-base-color has-accent-7-background-color has-text-color has-background wp-element-button" href="tel:+971585945676" style="border-radius:15px;font-size:1rem;font-weight:600;padding-top:var(--wp--preset--spacing--20);padding-right:var(--wp--preset--spacing--30);padding-bottom:var(--wp--preset--spacing--20);padding-left:var(--wp--preset--spacing--30)">📞 Call Us</a></div>
<!-- /wp:button -->

</div>
<!-- /wp:group -->

</div>
<!-- /wp:column -->

<!-- wp:column {"width":"50%"} -->
<div class="wp-block-column" style="flex-basis:50%">

<!-- wp:group {"style":{"spacing":{"padding":{"top":"var:preset|spacing|40","bottom":"var:preset|spacing|40","left":"var:preset|spacing|40","right":"var:preset|spacing|40"}},"border":{"radius":"15px"}},"backgroundColor":"base","layout":{"type":"constrained"}} -->
<div class="wp-block-group has-base-background-color has-background" style="border-radius:15px;padding-top:var(--wp--preset--spacing--40);padding-right:var(--wp--preset--spacing--40);padding-bottom:var(--wp--preset--spacing--40);padding-left:var(--wp--preset--spacing--40)">

<!-- wp:heading {"level":3,"style":{"typography":{"fontSize":"1.3rem","fontWeight":"600"}},"textColor":"accent-1"} -->
<h3 class="wp-block-heading has-accent-1-color has-text-color" style="font-size:1.3rem;font-weight:600">Contact Form</h3>
<!-- /wp:heading -->

<!-- wp:paragraph {"style":{"typography":{"fontSize":"0.9rem"}},"textColor":"accent-4"} -->
<p class="has-accent-4-color has-text-color" style="font-size:0.9rem">Fill out all fields below to get started</p>
<!-- /wp:paragraph -->

<!-- wp:group {"style":{"spacing":{"blockGap":"var:preset|spacing|30"}},"layout":{"type":"constrained"}} -->
<div class="wp-block-group">

<!-- wp:group {"layout":{"type":"constrained"}} -->
<div class="wp-block-group">

<!-- wp:paragraph {"style":{"typography":{"fontSize":"0.9rem","fontWeight":"600"}},"textColor":"contrast"} -->
<p class="has-contrast-color has-text-color" style="font-size:0.9rem;font-weight:600">Your name *</p>
<!-- /wp:paragraph -->

<!-- wp:paragraph {"style":{"spacing":{"padding":{"top":"var:preset|spacing|20","bottom":"var:preset|spacing|20","left":"var:preset|spacing|30","right":"var:preset|spacing|30"}},"border":{"radius":"8px","width":"1px"}},"borderColor":"accent-6","backgroundColor":"accent-6","textColor":"accent-4"} -->
<p class="has-border-color has-accent-6-border-color has-accent-4-color has-accent-6-background-color has-text-color has-background" style="border-width:1px;border-radius:8px;padding-top:var(--wp--preset--spacing--20);padding-right:var(--wp--preset--spacing--30);padding-bottom:var(--wp--preset--spacing--20);padding-left:var(--wp--preset--spacing--30)">Enter your full name</p>
<!-- /wp:paragraph -->

</div>
<!-- /wp:group -->

<!-- wp:group {"layout":{"type":"constrained"}} -->
<div class="wp-block-group">

<!-- wp:paragraph {"style":{"typography":{"fontSize":"0.9rem","fontWeight":"600"}},"textColor":"contrast"} -->
<p class="has-contrast-color has-text-color" style="font-size:0.9rem;font-weight:600">Your email *</p>
<!-- /wp:paragraph -->

<!-- wp:paragraph {"style":{"spacing":{"padding":{"top":"var:preset|spacing|20","bottom":"var:preset|spacing|20","left":"var:preset|spacing|30","right":"var:preset|spacing|30"}},"border":{"radius":"8px","width":"1px"}},"borderColor":"accent-6","backgroundColor":"accent-6","textColor":"accent-4"} -->
<p class="has-border-color has-accent-6-border-color has-accent-4-color has-accent-6-background-color has-text-color has-background" style="border-width:1px;border-radius:8px;padding-top:var(--wp--preset--spacing--20);padding-right:var(--wp--preset--spacing--30);padding-bottom:var(--wp--preset--spacing--20);padding-left:var(--wp--preset--spacing--30)">Enter your email address</p>
<!-- /wp:paragraph -->

</div>
<!-- /wp:group -->

<!-- wp:columns {"style":{"spacing":{"blockGap":{"left":"var:preset|spacing|20"}}}} -->
<div class="wp-block-columns">

<!-- wp:column -->
<div class="wp-block-column">

<!-- wp:paragraph {"style":{"typography":{"fontSize":"0.9rem","fontWeight":"600"}},"textColor":"contrast"} -->
<p class="has-contrast-color has-text-color" style="font-size:0.9rem;font-weight:600">UAE Phone Number *</p>
<!-- /wp:paragraph -->

<!-- wp:paragraph {"style":{"spacing":{"padding":{"top":"var:preset|spacing|20","bottom":"var:preset|spacing|20","left":"var:preset|spacing|30","right":"var:preset|spacing|30"}},"border":{"radius":"8px","width":"1px"}},"borderColor":"accent-6","backgroundColor":"accent-6","textColor":"accent-4"} -->
<p class="has-border-color has-accent-6-border-color has-accent-4-color has-accent-6-background-color has-text-color has-background" style="border-width:1px;border-radius:8px;padding-top:var(--wp--preset--spacing--20);padding-right:var(--wp--preset--spacing--30);padding-bottom:var(--wp--preset--spacing--20);padding-left:var(--wp--preset--spacing--30)">+971 xxx xxx xxx</p>
<!-- /wp:paragraph -->

</div>
<!-- /wp:column -->

<!-- wp:column -->
<div class="wp-block-column">

<!-- wp:paragraph {"style":{"typography":{"fontSize":"0.9rem","fontWeight":"600"}},"textColor":"contrast"} -->
<p class="has-contrast-color has-text-color" style="font-size:0.9rem;font-weight:600">Whatsapp *</p>
<!-- /wp:paragraph -->

<!-- wp:paragraph {"style":{"spacing":{"padding":{"top":"var:preset|spacing|20","bottom":"var:preset|spacing|20","left":"var:preset|spacing|30","right":"var:preset|spacing|30"}},"border":{"radius":"8px","width":"1px"}},"borderColor":"accent-6","backgroundColor":"accent-6","textColor":"accent-4"} -->
<p class="has-border-color has-accent-6-border-color has-accent-4-color has-accent-6-background-color has-text-color has-background" style="border-width:1px;border-radius:8px;padding-top:var(--wp--preset--spacing--20);padding-right:var(--wp--preset--spacing--30);padding-bottom:var(--wp--preset--spacing--20);padding-left:var(--wp--preset--spacing--30)">WhatsApp number</p>
<!-- /wp:paragraph -->

</div>
<!-- /wp:column -->

</div>
<!-- /wp:columns -->

<!-- wp:columns {"style":{"spacing":{"blockGap":{"left":"var:preset|spacing|20"}}}} -->
<div class="wp-block-columns">

<!-- wp:column -->
<div class="wp-block-column">

<!-- wp:paragraph {"style":{"typography":{"fontSize":"0.9rem","fontWeight":"600"}},"textColor":"contrast"} -->
<p class="has-contrast-color has-text-color" style="font-size:0.9rem;font-weight:600">Travelling to *</p>
<!-- /wp:paragraph -->

<!-- wp:paragraph {"style":{"spacing":{"padding":{"top":"var:preset|spacing|20","bottom":"var:preset|spacing|20","left":"var:preset|spacing|30","right":"var:preset|spacing|30"}},"border":{"radius":"8px","width":"1px"}},"borderColor":"accent-6","backgroundColor":"accent-6","textColor":"accent-4"} -->
<p class="has-border-color has-accent-6-border-color has-accent-4-color has-accent-6-background-color has-text-color has-background" style="border-width:1px;border-radius:8px;padding-top:var(--wp--preset--spacing--20);padding-right:var(--wp--preset--spacing--30);padding-bottom:var(--wp--preset--spacing--20);padding-left:var(--wp--preset--spacing--30)">Select destination</p>
<!-- /wp:paragraph -->

</div>
<!-- /wp:column -->

<!-- wp:column -->
<div class="wp-block-column">

<!-- wp:paragraph {"style":{"typography":{"fontSize":"0.9rem","fontWeight":"600"}},"textColor":"contrast"} -->
<p class="has-contrast-color has-text-color" style="font-size:0.9rem;font-weight:600">Nationality *</p>
<!-- /wp:paragraph -->

<!-- wp:paragraph {"style":{"spacing":{"padding":{"top":"var:preset|spacing|20","bottom":"var:preset|spacing|20","left":"var:preset|spacing|30","right":"var:preset|spacing|30"}},"border":{"radius":"8px","width":"1px"}},"borderColor":"accent-6","backgroundColor":"accent-6","textColor":"accent-4"} -->
<p class="has-border-color has-accent-6-border-color has-accent-4-color has-accent-6-background-color has-text-color has-background" style="border-width:1px;border-radius:8px;padding-top:var(--wp--preset--spacing--20);padding-right:var(--wp--preset--spacing--30);padding-bottom:var(--wp--preset--spacing--20);padding-left:var(--wp--preset--spacing--30)">Your nationality</p>
<!-- /wp:paragraph -->

</div>
<!-- /wp:column -->

</div>
<!-- /wp:columns -->

<!-- wp:columns {"style":{"spacing":{"blockGap":{"left":"var:preset|spacing|20"}}}} -->
<div class="wp-block-columns">

<!-- wp:column -->
<div class="wp-block-column">

<!-- wp:paragraph {"style":{"typography":{"fontSize":"0.9rem","fontWeight":"600"}},"textColor":"contrast"} -->
<p class="has-contrast-color has-text-color" style="font-size:0.9rem;font-weight:600">Profession on UAE residency visa *</p>
<!-- /wp:paragraph -->

<!-- wp:paragraph {"style":{"spacing":{"padding":{"top":"var:preset|spacing|20","bottom":"var:preset|spacing|20","left":"var:preset|spacing|30","right":"var:preset|spacing|30"}},"border":{"radius":"8px","width":"1px"}},"borderColor":"accent-6","backgroundColor":"accent-6","textColor":"accent-4"} -->
<p class="has-border-color has-accent-6-border-color has-accent-4-color has-accent-6-background-color has-text-color has-background" style="border-width:1px;border-radius:8px;padding-top:var(--wp--preset--spacing--20);padding-right:var(--wp--preset--spacing--30);padding-bottom:var(--wp--preset--spacing--20);padding-left:var(--wp--preset--spacing--30)">Your profession</p>
<!-- /wp:paragraph -->

</div>
<!-- /wp:column -->

<!-- wp:column -->
<div class="wp-block-column">

<!-- wp:paragraph {"style":{"typography":{"fontSize":"0.9rem","fontWeight":"600"}},"textColor":"contrast"} -->
<p class="has-contrast-color has-text-color" style="font-size:0.9rem;font-weight:600">Your salary range *</p>
<!-- /wp:paragraph -->

<!-- wp:paragraph {"style":{"spacing":{"padding":{"top":"var:preset|spacing|20","bottom":"var:preset|spacing|20","left":"var:preset|spacing|30","right":"var:preset|spacing|30"}},"border":{"radius":"8px","width":"1px"}},"borderColor":"accent-6","backgroundColor":"accent-6","textColor":"accent-4"} -->
<p class="has-border-color has-accent-6-border-color has-accent-4-color has-accent-6-background-color has-text-color has-background" style="border-width:1px;border-radius:8px;padding-top:var(--wp--preset--spacing--20);padding-right:var(--wp--preset--spacing--30);padding-bottom:var(--wp--preset--spacing--20);padding-left:var(--wp--preset--spacing--30)">Above 8000 AED / Below 8000 AED</p>
<!-- /wp:paragraph -->

</div>
<!-- /wp:column -->

</div>
<!-- /wp:columns -->

<!-- wp:button {"style":{"border":{"radius":"25px"},"typography":{"fontWeight":"700","fontSize":"1rem","textTransform":"uppercase","letterSpacing":"0.5px"},"spacing":{"padding":{"left":"var:preset|spacing|50","right":"var:preset|spacing|50","top":"var:preset|spacing|30","bottom":"var:preset|spacing|30"}}},"className":"is-style-fill","width":100} -->
<div class="wp-block-button has-custom-width wp-block-button__width-100 is-style-fill"><a class="wp-block-button__link wp-element-button" style="border-radius:25px;font-size:1rem;font-weight:700;letter-spacing:0.5px;padding-top:var(--wp--preset--spacing--30);padding-right:var(--wp--preset--spacing--50);padding-bottom:var(--wp--preset--spacing--30);padding-left:var(--wp--preset--spacing--50);text-transform:uppercase">Submit Application</a></div>
<!-- /wp:button -->

</div>
<!-- /wp:group -->

</div>
<!-- /wp:group -->

</div>
<!-- /wp:column -->

</div>
<!-- /wp:columns -->

</div>
<!-- /wp:group -->
