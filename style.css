/*
Theme Name: Visa Guy Custom Theme
Theme URI: https://visaguy.ae/
Author: Visa Guy Team
Author URI: https://visaguy.ae/
Description: A professional WordPress theme inspired by Visa Guy UAE's design. Features a clean, modern layout optimized for visa services, immigration consultancy, and professional service businesses. Includes specialized patterns for service listings, testimonials, contact forms, and call-to-action sections. The theme emphasizes trust, professionalism, and user-friendly navigation with a blue and white color scheme that conveys reliability and expertise in visa assistance services.
Requires at least: 6.7
Tested up to: 6.8
Requires PHP: 7.2
Version: 1.0
License: GNU General Public License v2 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html
Text Domain: visa-guy-theme
Tags: business, professional, services, visa, immigration, consulting, blue, white, clean, modern, responsive, full-site-editing, block-patterns, custom-colors, custom-logo, featured-images, accessibility-ready
*/

/*
 * Link styles
 * https://github.com/WordPress/gutenberg/issues/42319
 */
a {
	text-decoration-thickness: 1px !important;
	text-underline-offset: .1em;
}

/* Focus styles */
:where(.wp-site-blocks *:focus) {
	outline-width: 2px;
	outline-style: solid;
}

/* Increase the bottom margin on submenus, so that the outline is visible. */
.wp-block-navigation .wp-block-navigation-submenu .wp-block-navigation-item:not(:last-child) {
	margin-bottom: 3px;
}

/* Increase the outline offset on the parent menu items, so that the outline does not touch the text. */
.wp-block-navigation .wp-block-navigation-item .wp-block-navigation-item__content {
	outline-offset: 4px;
}

/* Remove outline offset from the submenus, otherwise the outline is visible outside the submenu container. */
.wp-block-navigation .wp-block-navigation-item ul.wp-block-navigation__submenu-container .wp-block-navigation-item__content {
	outline-offset: 0;
}

/*
 * Progressive enhancement to reduce widows and orphans
 * https://github.com/WordPress/gutenberg/issues/55190
 */
h1, h2, h3, h4, h5, h6, blockquote, caption, figcaption, p {
	text-wrap: pretty;
}

/*
 * Change the position of the more block on the front, by making it a block level element.
 * https://github.com/WordPress/gutenberg/issues/65934
*/
.more-link {
	display: block;
}

/* Visa Guy Custom Styles */

/* Professional Button Styles */
.wp-block-button__link {
	border-radius: 25px !important;
	padding: 12px 30px !important;
	font-weight: 600 !important;
	text-transform: uppercase !important;
	letter-spacing: 0.5px !important;
	transition: all 0.3s ease !important;
}

.wp-block-button.is-style-fill .wp-block-button__link {
	background: linear-gradient(135deg, #0066cc 0%, #004499 100%) !important;
}

.wp-block-button.is-style-fill .wp-block-button__link:hover {
	background: linear-gradient(135deg, #004499 0%, #0066cc 100%) !important;
	transform: translateY(-2px) !important;
	box-shadow: 0 8px 25px rgba(0, 102, 204, 0.3) !important;
}

/* WhatsApp Style Button */
.wp-block-button.is-style-whatsapp .wp-block-button__link {
	background: #25d366 !important;
	color: white !important;
}

.wp-block-button.is-style-whatsapp .wp-block-button__link:hover {
	background: #128c7e !important;
	transform: translateY(-2px) !important;
	box-shadow: 0 8px 25px rgba(37, 211, 102, 0.3) !important;
}

/* Call Button Style */
.wp-block-button.is-style-call .wp-block-button__link {
	background: #ff6b35 !important;
	color: white !important;
}

.wp-block-button.is-style-call .wp-block-button__link:hover {
	background: #e55a2b !important;
	transform: translateY(-2px) !important;
	box-shadow: 0 8px 25px rgba(255, 107, 53, 0.3) !important;
}

/* Service Cards */
.visa-service-card {
	background: white;
	border-radius: 15px;
	padding: 30px;
	box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
	transition: all 0.3s ease;
	border: 1px solid #e6f3ff;
}

.visa-service-card:hover {
	transform: translateY(-5px);
	box-shadow: 0 15px 40px rgba(0, 102, 204, 0.15);
}

/* Professional Header Styles */
.site-header {
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(10px);
	border-bottom: 1px solid #e6f3ff;
}

/* Hero Section */
.hero-section {
	background: linear-gradient(135deg, #e6f3ff 0%, #ffffff 100%);
	padding: 80px 0;
}

/* Testimonial Styles */
.testimonial-card {
	background: #f8f9fa;
	border-left: 4px solid #0066cc;
	padding: 25px;
	border-radius: 10px;
	margin: 20px 0;
}

/* FAQ Styles */
.faq-item {
	border-bottom: 1px solid #e6f3ff;
	padding: 20px 0;
}

.faq-question {
	font-weight: 600;
	color: #0066cc;
	margin-bottom: 10px;
}

/* Country Flag Icons */
.country-flag {
	width: 30px;
	height: 20px;
	border-radius: 3px;
	margin-right: 10px;
	display: inline-block;
}

/* Professional Typography */
.professional-heading {
	color: #1a1a1a;
	font-weight: 700;
	line-height: 1.2;
}

.professional-subheading {
	color: #0066cc;
	font-weight: 600;
	text-transform: uppercase;
	letter-spacing: 1px;
	font-size: 14px;
	margin-bottom: 10px;
}

/* Contact Information Styles */
.contact-info {
	background: #0066cc;
	color: white;
	padding: 15px 25px;
	border-radius: 10px;
	display: inline-block;
	margin: 10px;
}

.contact-info a {
	color: white;
	text-decoration: none;
	font-weight: 600;
}

.contact-info:hover {
	background: #004499;
	transform: translateY(-2px);
}

/* Mobile Responsive Adjustments */
@media (max-width: 768px) {
	.hero-section {
		padding: 50px 0;
	}

	.wp-block-button__link {
		padding: 10px 20px !important;
		font-size: 14px !important;
	}

	.visa-service-card {
		padding: 20px;
		margin-bottom: 20px;
	}
}
