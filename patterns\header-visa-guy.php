<?php
/**
 * Title: White Wings Professional Header
 * Slug: white-wings-theme/header-white-wings
 * Categories: header
 * Description: Professional header with White Wings logo, navigation, and contact information for visa services - Reach New Heights
 */
?>

<!-- wp:group {"align":"full","style":{"spacing":{"padding":{"top":"var:preset|spacing|30","bottom":"var:preset|spacing|30","left":"var:preset|spacing|50","right":"var:preset|spacing|50"},"margin":{"top":"0","bottom":"0"}},"color":{"background":"rgba(255,255,255,0.95)"}},"className":"site-header","layout":{"type":"constrained","wideSize":"1200px"}} -->
<div class="wp-block-group alignfull site-header has-background" style="background:rgba(255,255,255,0.95);margin-top:0;margin-bottom:0;padding-top:var(--wp--preset--spacing--30);padding-right:var(--wp--preset--spacing--50);padding-bottom:var(--wp--preset--spacing--30);padding-left:var(--wp--preset--spacing--50)">

<!-- wp:group {"style":{"spacing":{"blockGap":"var:preset|spacing|30"}},"layout":{"type":"flex","flexWrap":"wrap","justifyContent":"space-between"}} -->
<div class="wp-block-group">

<!-- wp:group {"style":{"spacing":{"blockGap":"var:preset|spacing|40"}},"layout":{"type":"flex","flexWrap":"nowrap"}} -->
<div class="wp-block-group">

<!-- wp:site-logo {"width":60,"shouldSyncIcon":false} /-->

<!-- wp:group {"layout":{"type":"constrained"}} -->
<div class="wp-block-group">

<!-- wp:site-title {"style":{"typography":{"fontWeight":"700","fontSize":"1.5rem","textTransform":"uppercase","letterSpacing":"1px"}},"textColor":"accent-1"} /-->

<!-- wp:paragraph {"style":{"typography":{"fontSize":"0.9rem","textTransform":"uppercase","letterSpacing":"0.5px","fontWeight":"500"}},"textColor":"accent-4"} -->
<p class="has-accent-4-color has-text-color" style="font-size:0.9rem;font-weight:500;letter-spacing:0.5px;text-transform:uppercase">Reach New Heights</p>
<!-- /wp:paragraph -->

</div>
<!-- /wp:group -->

</div>
<!-- /wp:group -->

<!-- wp:navigation {"textColor":"contrast","overlayBackgroundColor":"base","overlayTextColor":"contrast","style":{"typography":{"fontWeight":"500","fontSize":"1rem"},"spacing":{"blockGap":"var:preset|spacing|40"}},"layout":{"type":"flex","setCascadingProperties":true,"justifyContent":"center"}} -->
<!-- wp:navigation-link {"label":"Home","type":"","url":"#","kind":"custom","isTopLevelLink":true} /-->
<!-- wp:navigation-link {"label":"About","type":"","url":"#","kind":"custom","isTopLevelLink":true} /-->
<!-- wp:navigation-link {"label":"Countries","type":"","url":"#","kind":"custom","isTopLevelLink":true} /-->
<!-- wp:navigation-link {"label":"Services","type":"","url":"#","kind":"custom","isTopLevelLink":true} /-->
<!-- wp:navigation-link {"label":"Contact","type":"","url":"#","kind":"custom","isTopLevelLink":true} /-->
<!-- /wp:navigation -->

<!-- wp:group {"style":{"spacing":{"blockGap":"var:preset|spacing|20"}},"layout":{"type":"flex","flexWrap":"nowrap"}} -->
<div class="wp-block-group">

<!-- wp:button {"backgroundColor":"accent-8","textColor":"base","style":{"border":{"radius":"20px"},"typography":{"fontWeight":"600","fontSize":"0.9rem","textTransform":"uppercase","letterSpacing":"0.5px"},"spacing":{"padding":{"left":"var:preset|spacing|30","right":"var:preset|spacing|30","top":"var:preset|spacing|20","bottom":"var:preset|spacing|20"}}},"className":"is-style-whatsapp"} -->
<div class="wp-block-button is-style-whatsapp"><a class="wp-block-button__link has-base-color has-accent-8-background-color has-text-color has-background wp-element-button" style="border-radius:20px;font-size:0.9rem;font-weight:600;letter-spacing:0.5px;padding-top:var(--wp--preset--spacing--20);padding-right:var(--wp--preset--spacing--30);padding-bottom:var(--wp--preset--spacing--20);padding-left:var(--wp--preset--spacing--30);text-transform:uppercase">WhatsApp</a></div>
<!-- /wp:button -->

<!-- wp:button {"backgroundColor":"accent-7","textColor":"base","style":{"border":{"radius":"20px"},"typography":{"fontWeight":"600","fontSize":"0.9rem","textTransform":"uppercase","letterSpacing":"0.5px"},"spacing":{"padding":{"left":"var:preset|spacing|30","right":"var:preset|spacing|30","top":"var:preset|spacing|20","bottom":"var:preset|spacing|20"}}},"className":"is-style-call"} -->
<div class="wp-block-button is-style-call"><a class="wp-block-button__link has-base-color has-accent-7-background-color has-text-color has-background wp-element-button" style="border-radius:20px;font-size:0.9rem;font-weight:600;letter-spacing:0.5px;padding-top:var(--wp--preset--spacing--20);padding-right:var(--wp--preset--spacing--30);padding-bottom:var(--wp--preset--spacing--20);padding-left:var(--wp--preset--spacing--30);text-transform:uppercase">Call Us</a></div>
<!-- /wp:button -->

</div>
<!-- /wp:group -->

</div>
<!-- /wp:group -->

</div>
<!-- /wp:group -->

<!-- wp:separator {"style":{"spacing":{"margin":{"top":"0","bottom":"0"}}},"backgroundColor":"accent-6","className":"is-style-wide"} -->
<hr class="wp-block-separator has-text-color has-accent-6-color has-alpha-channel-opacity has-accent-6-background-color has-background is-style-wide" style="margin-top:0;margin-bottom:0"/>
<!-- /wp:separator -->
