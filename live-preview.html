<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>White Wings - Reach New Heights | Professional Visa Services UAE</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #1a1a1a;
            overflow-x: hidden;
        }
        
        /* Top Contact Bar */
        .top-contact-bar {
            background: linear-gradient(135deg, #00bfff 0%, #0080ff 100%);
            color: white;
            padding: 10px 0;
            font-size: 14px;
        }
        
        .top-contact-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .contact-info {
            display: flex;
            gap: 30px;
            align-items: center;
        }
        
        .contact-info span {
            font-weight: 500;
        }
        
        .top-whatsapp {
            background: rgba(255, 255, 255, 0.2);
            padding: 8px 15px;
            border-radius: 15px;
            text-decoration: none;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .top-whatsapp:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-1px);
        }
        
        /* Header */
        .site-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid #e6f7ff;
            padding: 15px 0;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: 0 2px 10px rgba(0, 191, 255, 0.1);
        }
        
        .header-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .logo-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #00bfff 0%, #0080ff 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 30px;
            box-shadow: 0 5px 15px rgba(0, 191, 255, 0.3);
        }
        
        .logo::before {
            content: '✈️';
        }
        
        .brand-text h1 {
            font-size: 1.8rem;
            font-weight: 700;
            color: #00bfff;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin: 0;
        }
        
        .brand-text .tagline {
            font-size: 1rem;
            color: #0080ff;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-weight: 600;
        }
        
        .nav-menu {
            display: flex;
            list-style: none;
            gap: 30px;
            align-items: center;
        }
        
        .nav-menu a {
            text-decoration: none;
            color: #1a1a1a;
            font-weight: 500;
            font-size: 16px;
            transition: color 0.3s ease;
            position: relative;
        }
        
        .nav-menu a:hover {
            color: #00bfff;
        }
        
        .nav-menu a::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 0;
            height: 2px;
            background: #00bfff;
            transition: width 0.3s ease;
        }
        
        .nav-menu a:hover::after {
            width: 100%;
        }
        
        .apply-btn {
            background: linear-gradient(135deg, #00bfff 0%, #0080ff 100%);
            color: white;
            padding: 12px 25px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 191, 255, 0.3);
        }
        
        .apply-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 191, 255, 0.4);
        }
        
        /* Hero Section */
        .hero-section {
            background: linear-gradient(135deg, #e6f7ff 0%, #ffffff 100%);
            padding: 100px 0;
            position: relative;
            overflow: hidden;
        }
        
        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 30% 50%, rgba(0, 191, 255, 0.1) 0%, transparent 50%);
            pointer-events: none;
        }
        
        .hero-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 60px;
            align-items: center;
            position: relative;
            z-index: 1;
        }
        
        .hero-content .hero-subtitle {
            color: #00bfff;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-size: 14px;
            margin-bottom: 15px;
        }
        
        .hero-content h1 {
            font-size: clamp(3rem, 6vw, 5rem);
            font-weight: 700;
            line-height: 1.1;
            color: #1a1a1a;
            margin-bottom: 25px;
        }
        
        .hero-description {
            font-size: 1.2rem;
            line-height: 1.6;
            color: #666666;
            margin-bottom: 40px;
        }
        
        .hero-buttons {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #00bfff 0%, #0080ff 100%);
            color: white;
            padding: 18px 35px;
            border-radius: 30px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 1.1rem;
            box-shadow: 0 6px 20px rgba(0, 191, 255, 0.3);
        }
        
        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(0, 191, 255, 0.4);
        }
        
        .btn-call {
            background: #ff6b35;
            color: white;
            padding: 18px 35px;
            border-radius: 30px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 1.1rem;
            box-shadow: 0 6px 20px rgba(255, 107, 53, 0.3);
        }
        
        .btn-call:hover {
            background: #e55a2b;
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(255, 107, 53, 0.4);
        }
        
        .hero-image {
            text-align: center;
            position: relative;
        }
        
        .hero-image img {
            max-width: 100%;
            border-radius: 20px;
            box-shadow: 0 20px 50px rgba(0, 191, 255, 0.2);
            transition: transform 0.3s ease;
        }
        
        .hero-image:hover img {
            transform: scale(1.02);
        }
        
        /* Country Carousel */
        .country-carousel {
            padding: 80px 0;
            background: #f8fdff;
        }
        
        .carousel-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
            text-align: center;
        }
        
        .section-title {
            font-size: clamp(2rem, 4vw, 3rem);
            font-weight: 700;
            color: #1a1a1a;
            margin-bottom: 20px;
        }
        
        .section-description {
            font-size: 1.1rem;
            color: #666666;
            margin-bottom: 50px;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .country-banners {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 50px;
        }
        
        .country-banner {
            position: relative;
            border-radius: 15px;
            overflow: hidden;
            height: 120px;
            background: linear-gradient(135deg, #00bfff 0%, #0080ff 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 700;
            font-size: 1.2rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .country-banner:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 191, 255, 0.3);
        }
        
        .country-banner::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
        }

        /* Country Grid */
        .country-grid {
            padding: 80px 0;
            background: white;
        }

        .grid-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .countries-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 30px;
            margin-bottom: 50px;
        }

        .country-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.4s ease;
            border: 1px solid #e6f7ff;
            position: relative;
            overflow: hidden;
        }

        .country-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 191, 255, 0.1), transparent);
            transition: left 0.6s ease;
        }

        .country-card:hover::before {
            left: 100%;
        }

        .country-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 50px rgba(0, 191, 255, 0.2);
            border-color: #00bfff;
        }

        .country-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
        }

        .country-flag {
            width: 50px;
            height: 35px;
            border-radius: 5px;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
        }

        .country-card h3 {
            font-size: 1.3rem;
            font-weight: 700;
            color: #00bfff;
            margin: 0;
        }

        .country-card p {
            color: #666666;
            font-size: 0.9rem;
            margin: 0;
        }

        /* Services Section */
        .services-section {
            padding: 100px 0;
            background: #f8fdff;
        }

        .services-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .services-intro {
            text-align: center;
            margin-bottom: 60px;
        }

        .services-intro h2 {
            font-size: clamp(2rem, 4vw, 3rem);
            font-weight: 700;
            color: #1a1a1a;
            margin-bottom: 20px;
        }

        .services-intro p {
            font-size: 1.1rem;
            color: #666666;
            max-width: 800px;
            margin: 0 auto;
            line-height: 1.7;
        }

        /* Doorstep Service */
        .doorstep-service {
            padding: 100px 0;
            background: white;
        }

        .doorstep-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 60px;
            align-items: center;
        }

        .doorstep-image img {
            width: 100%;
            border-radius: 20px;
            box-shadow: 0 15px 40px rgba(0, 191, 255, 0.15);
        }

        .doorstep-content .service-badge {
            background: linear-gradient(135deg, #00bfff 0%, #0080ff 100%);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            display: inline-block;
            margin-bottom: 20px;
        }

        .doorstep-content h2 {
            font-size: 2.2rem;
            font-weight: 700;
            color: #1a1a1a;
            margin-bottom: 20px;
            line-height: 1.2;
        }

        .doorstep-content p {
            font-size: 1.1rem;
            color: #666666;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .service-features {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }

        .service-features ul {
            list-style: none;
            padding: 0;
        }

        .service-features li {
            padding: 8px 0;
            color: #666666;
            position: relative;
            padding-left: 25px;
        }

        .service-features li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #00bfff;
            font-weight: bold;
        }

        .service-buttons {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .btn-secondary {
            background: transparent;
            color: #00bfff;
            border: 2px solid #00bfff;
            padding: 12px 25px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
        }

        .btn-secondary:hover {
            background: #00bfff;
            color: white;
            transform: translateY(-2px);
        }

        /* Testimonials */
        .testimonials-section {
            padding: 100px 0;
            background: #f8fdff;
        }

        .testimonials-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            text-align: center;
        }

        .testimonial-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            margin: 0 auto;
            max-width: 600px;
            position: relative;
        }

        .testimonial-card::before {
            content: '"';
            font-size: 4rem;
            color: #00bfff;
            position: absolute;
            top: 10px;
            left: 20px;
            font-family: serif;
        }

        .testimonial-text {
            font-size: 1.1rem;
            color: #666666;
            line-height: 1.7;
            margin-bottom: 30px;
            font-style: italic;
        }

        .testimonial-author {
            font-weight: 700;
            color: #00bfff;
            font-size: 1.1rem;
        }

        .testimonial-date {
            color: #999999;
            font-size: 0.9rem;
            margin-top: 5px;
        }

        /* Media Section */
        .media-section {
            padding: 80px 0;
            background: white;
        }

        .media-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            text-align: center;
        }

        .media-logos {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 40px;
            flex-wrap: wrap;
            margin-top: 40px;
        }

        .media-logo {
            width: 120px;
            height: 60px;
            background: #f0f0f0;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            color: #666666;
            transition: all 0.3s ease;
        }

        .media-logo:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 191, 255, 0.2);
        }

        /* FAQ Section */
        .faq-section {
            padding: 100px 0;
            background: #f8fdff;
        }

        .faq-container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .faq-item {
            background: white;
            border-radius: 15px;
            margin-bottom: 20px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .faq-question {
            padding: 25px;
            font-weight: 700;
            color: #00bfff;
            font-size: 1.1rem;
            cursor: pointer;
            border-bottom: 1px solid #e6f7ff;
        }

        .faq-answer {
            padding: 25px;
            color: #666666;
            line-height: 1.7;
        }

        /* Contact Form */
        .contact-form-section {
            padding: 100px 0;
            background: white;
        }

        .contact-form-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 60px;
            align-items: center;
        }

        .contact-info h2 {
            font-size: 2.5rem;
            font-weight: 700;
            color: #1a1a1a;
            margin-bottom: 20px;
        }

        .contact-info p {
            font-size: 1.1rem;
            color: #666666;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .social-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-bottom: 30px;
        }

        .social-btn {
            padding: 8px 15px;
            border-radius: 20px;
            text-decoration: none;
            font-weight: 600;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .whatsapp { background: #25d366; color: white; }
        .instagram { background: #e4405f; color: white; }
        .facebook { background: #1877f2; color: white; }
        .linkedin { background: #0077b5; color: white; }
        .twitter { background: #1da1f2; color: white; }
        .snapchat { background: #fffc00; color: black; }
        .youtube { background: #ff0000; color: white; }

        .social-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .contact-form {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #1a1a1a;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e6f7ff;
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #00bfff;
            box-shadow: 0 0 0 3px rgba(0, 191, 255, 0.1);
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .submit-btn {
            width: 100%;
            background: linear-gradient(135deg, #00bfff 0%, #0080ff 100%);
            color: white;
            padding: 15px;
            border: none;
            border-radius: 25px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 191, 255, 0.3);
        }

        /* CTA Section */
        .cta-section {
            padding: 100px 0;
            background: linear-gradient(135deg, #00bfff 0%, #0080ff 100%);
            color: white;
            text-align: center;
        }

        .cta-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .cta-section h2 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 20px;
        }

        .cta-section p {
            font-size: 1.2rem;
            margin-bottom: 40px;
            line-height: 1.6;
        }

        .cta-btn {
            background: white;
            color: #00bfff;
            padding: 18px 40px;
            border-radius: 30px;
            text-decoration: none;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            font-size: 1.1rem;
            display: inline-block;
        }

        .cta-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(255, 255, 255, 0.3);
        }

        /* Footer */
        .footer {
            background: #1a1a1a;
            color: white;
            padding: 80px 0 30px;
        }

        .footer-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .footer-grid {
            display: grid;
            grid-template-columns: 2fr 1fr 2fr;
            gap: 60px;
            margin-bottom: 40px;
        }

        .footer-brand h3 {
            font-size: 2rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 10px;
            color: #00bfff;
        }

        .footer-brand .brand-tagline {
            font-size: 1.1rem;
            font-weight: 600;
            color: #0080ff;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 20px;
        }

        .footer-brand p {
            line-height: 1.6;
            margin-bottom: 15px;
            color: #cccccc;
        }

        .footer-links h4 {
            font-size: 1.2rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 20px;
            color: #00bfff;
        }

        .footer-links ul {
            list-style: none;
        }

        .footer-links li {
            margin-bottom: 10px;
        }

        .footer-links a {
            color: #cccccc;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .footer-links a:hover {
            color: #00bfff;
        }

        .footer-contact h4 {
            font-size: 1.2rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 20px;
            color: #00bfff;
        }

        .footer-contact p {
            color: #cccccc;
            margin-bottom: 10px;
        }

        .footer-contact strong {
            color: #00bfff;
        }

        .footer-bottom {
            border-top: 1px solid #333333;
            padding-top: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }

        .footer-bottom p {
            color: #666666;
            font-size: 0.9rem;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .top-contact-container {
                flex-direction: column;
                gap: 10px;
                text-align: center;
            }

            .contact-info {
                flex-direction: column;
                gap: 10px;
            }

            .header-container {
                flex-direction: column;
                gap: 20px;
                text-align: center;
            }

            .nav-menu {
                flex-wrap: wrap;
                justify-content: center;
                gap: 15px;
            }

            .hero-container {
                grid-template-columns: 1fr;
                text-align: center;
                gap: 40px;
            }

            .hero-section {
                padding: 60px 0;
            }

            .country-banners {
                grid-template-columns: repeat(2, 1fr);
            }

            .countries-grid {
                grid-template-columns: 1fr;
            }

            .doorstep-container {
                grid-template-columns: 1fr;
                text-align: center;
            }

            .contact-form-container {
                grid-template-columns: 1fr;
            }

            .service-features {
                grid-template-columns: 1fr;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .footer-grid {
                grid-template-columns: 1fr;
                gap: 40px;
            }

            .footer-bottom {
                flex-direction: column;
                gap: 10px;
                text-align: center;
            }

            .hero-buttons,
            .service-buttons {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <!-- Top Contact Bar -->
    <div class="top-contact-bar">
        <div class="top-contact-container">
            <div class="contact-info">
                <span>📞 Call Us: +971 525 263 232</span>
                <span>📧 <EMAIL></span>
            </div>
            <a href="https://api.whatsapp.com/send/?phone=%2B971*********&text=Hi%20White%20Wings,%20could%20you%20tell%20me%20more%20about%20your%20services?" class="top-whatsapp">WhatsApp Us</a>
        </div>
    </div>

    <!-- Header -->
    <header class="site-header">
        <div class="header-container">
            <div class="logo-section">
                <div class="logo"></div>
                <div class="brand-text">
                    <h1>White Wings</h1>
                    <div class="tagline">Reach New Heights</div>
                </div>
            </div>

            <nav>
                <ul class="nav-menu">
                    <li><a href="#home">Home</a></li>
                    <li><a href="#about">About</a></li>
                    <li><a href="#countries">Countries</a></li>
                    <li><a href="#corporate">Corporate Visa</a></li>
                    <li><a href="#blog">Blog</a></li>
                    <li><a href="#news">News</a></li>
                    <li><a href="#contact">Contact</a></li>
                </ul>
            </nav>

            <a href="#contact" class="apply-btn">Apply Now</a>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero-section" id="home">
        <div class="hero-container">
            <div class="hero-content">
                <div class="hero-subtitle">Professional Visa Services UAE</div>
                <h1>Visa Simplified.</h1>
                <p class="hero-description">
                    Navigating the visa application process can be complex and time-consuming.
                    But don't worry, we're here to help! At White Wings, we specialize in
                    making visa applications simple and stress-free. Contact us right now!
                </p>
                <div class="hero-buttons">
                    <a href="https://api.whatsapp.com/send/?phone=%2B971*********&text=Hi%20White%20Wings,%20could%20you%20tell%20me%20more%20about%20your%20services?" class="btn-primary">📱 Book Your Free Consultation!</a>
                    <a href="tel:+971*********" class="btn-call">📞 Call Us Now</a>
                </div>
            </div>
            <div class="hero-image">
                <img src="https://images.unsplash.com/photo-1436491865332-7a61a109cc05?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80" alt="Professional visa consultation services - Airplane flying over beautiful landscape">
            </div>
        </div>
    </section>

    <!-- Country Carousel -->
    <section class="country-carousel">
        <div class="carousel-container">
            <h2 class="section-title">Popular Destinations</h2>
            <p class="section-description">Explore visa services for the world's most sought-after destinations</p>

            <div class="country-banners">
                <div class="country-banner">🇯🇵 Japan Visa</div>
                <div class="country-banner">🇺🇸 USA Visa</div>
                <div class="country-banner">🇬🇧 UK Visa</div>
                <div class="country-banner">🇨🇦 Canada Visa</div>
                <div class="country-banner">🇮🇹 Italy Visa</div>
                <div class="country-banner">🇳🇱 Netherlands Visa</div>
            </div>
        </div>
    </section>

    <!-- Country Grid -->
    <section class="country-grid" id="countries">
        <div class="grid-container">
            <h2 class="section-title">All Countries We Serve</h2>
            <p class="section-description">Professional visa assistance for destinations worldwide</p>

            <div class="countries-grid">
                <div class="country-card">
                    <div class="country-header">
                        <div class="country-flag">🇲🇳</div>
                        <div>
                            <h3>Mongolia Visa</h3>
                            <p>For UAE Residents</p>
                        </div>
                    </div>
                </div>

                <div class="country-card">
                    <div class="country-header">
                        <div class="country-flag">🇰🇪</div>
                        <div>
                            <h3>Kenya Visa</h3>
                            <p>For UAE Residents</p>
                        </div>
                    </div>
                </div>

                <div class="country-card">
                    <div class="country-header">
                        <div class="country-flag">🇮🇩</div>
                        <div>
                            <h3>Indonesia E-Visa</h3>
                            <p>From UAE</p>
                        </div>
                    </div>
                </div>

                <div class="country-card">
                    <div class="country-header">
                        <div class="country-flag">🇧🇬</div>
                        <div>
                            <h3>Bulgaria Visa</h3>
                            <p>For UAE Residents</p>
                        </div>
                    </div>
                </div>

                <div class="country-card">
                    <div class="country-header">
                        <div class="country-flag">🇲🇾</div>
                        <div>
                            <h3>Malaysia Visa</h3>
                            <p>For UAE Residents</p>
                        </div>
                    </div>
                </div>

                <div class="country-card">
                    <div class="country-header">
                        <div class="country-flag">🇧🇷</div>
                        <div>
                            <h3>Brazil Tourist Visa</h3>
                            <p>For UAE Residents</p>
                        </div>
                    </div>
                </div>

                <div class="country-card">
                    <div class="country-header">
                        <div class="country-flag">🇲🇦</div>
                        <div>
                            <h3>Morocco Visa</h3>
                            <p>For UAE Residents</p>
                        </div>
                    </div>
                </div>

                <div class="country-card">
                    <div class="country-header">
                        <div class="country-flag">🇹🇭</div>
                        <div>
                            <h3>Thailand Visa</h3>
                            <p>For UAE Residents</p>
                        </div>
                    </div>
                </div>

                <div class="country-card">
                    <div class="country-header">
                        <div class="country-flag">🇦🇲</div>
                        <div>
                            <h3>Armenia Visa</h3>
                            <p>For UAE Residents</p>
                        </div>
                    </div>
                </div>

                <div class="country-card">
                    <div class="country-header">
                        <div class="country-flag">🇵🇹</div>
                        <div>
                            <h3>Portugal Visa</h3>
                            <p>For UAE Residents</p>
                        </div>
                    </div>
                </div>

                <div class="country-card">
                    <div class="country-header">
                        <div class="country-flag">🇰🇬</div>
                        <div>
                            <h3>Kyrgyzstan Visa</h3>
                            <p>For UAE Residents</p>
                        </div>
                    </div>
                </div>

                <div class="country-card">
                    <div class="country-header">
                        <div class="country-flag">🇻🇳</div>
                        <div>
                            <h3>Vietnam Visa</h3>
                            <p>For UAE Residents</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section class="services-section">
        <div class="services-container">
            <div class="services-intro">
                <h2>White Wings Visa Assistance Services UAE</h2>
                <p>White Wings (WW) is a trusted visa service in Dubai that provides global visa services for citizens and residents in the UAE. With White Wings, you can find answers to your visa questions. Our service is global, and we can help with any kind of visa assistance. We ensure that you get the most value for money in your visa application process.</p>
            </div>
        </div>
    </section>

    <!-- Doorstep Service -->
    <section class="doorstep-service">
        <div class="doorstep-container">
            <div class="doorstep-image">
                <img src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80" alt="Professional doorstep visa service">
            </div>
            <div class="doorstep-content">
                <div class="service-badge">✅ All Inclusive Visa Service</div>
                <h2>Visa Assistance At Your Office Door Step</h2>
                <p>Preparing documents and delivering them at their location, explaining the process one-on-one.</p>
                <p><strong>We're here to simplify the process by bringing the visa application right to your office doorstep. Our mission is to make obtaining business travel visas seamless and convenient so you can focus on what matters most - your business.</strong></p>

                <div class="service-features">
                    <ul>
                        <li>Appointment Booking</li>
                        <li>Flight Reservation For Visa Purpose</li>
                        <li>Covering Letter (Embassy)</li>
                    </ul>
                    <ul>
                        <li>Application Form Filling</li>
                        <li>Hotel Reservation For Visa Purpose</li>
                        <li>Detailed Trip Itinerary</li>
                    </ul>
                </div>

                <div class="service-buttons">
                    <a href="#contact" class="btn-primary">Know More!</a>
                    <a href="tel:+971585970989" class="btn-secondary">Call Us</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section class="testimonials-section">
        <div class="testimonials-container">
            <h2 class="section-title">What our customers say</h2>
            <div class="testimonial-card">
                <p class="testimonial-text">White Wings' service was exceptional. They helped me get my Ireland visa. They were so helpful and took care of everything from start to finish and I got my visa without having to worry about anything.</p>
                <div class="testimonial-author">Akhilesh Viswanath</div>
                <div class="testimonial-date">2024-06-25</div>
            </div>
        </div>
    </section>

    <!-- What Sets Us Apart -->
    <section class="doorstep-service">
        <div class="doorstep-container">
            <div class="doorstep-content">
                <div class="service-badge">✅ All Inclusive Visa Service</div>
                <h2>What Sets Us Apart From Other Visa Services in UAE?</h2>
                <p>The visa service is available at your convenience and will always be there to assist you with your application. Our dedicated team will guide you through the process at every step.</p>

                <div class="service-features">
                    <ul>
                        <li>We offer convenient, efficient, and prompt visa assistance in UAE</li>
                        <li>We offer a wide range of services, so you can find the one that fits</li>
                        <li>Our visa consultants will review all of your details</li>
                        <li>As a visa company in UAE, we have a strong track record of success</li>
                    </ul>
                </div>

                <div class="service-buttons">
                    <a href="#contact" class="btn-primary">Apply Now</a>
                </div>
            </div>
            <div class="doorstep-image">
                <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80" alt="Visa Services in Dubai UAE">
            </div>
        </div>
    </section>

    <!-- Media Section -->
    <section class="media-section">
        <div class="media-container">
            <h2 class="section-title">We Featured In:</h2>
            <div class="media-logos">
                <div class="media-logo">Khaleej Times</div>
                <div class="media-logo">Gulf News</div>
                <div class="media-logo">Emirates 24/7</div>
                <div class="media-logo">Arabian Business</div>
                <div class="media-logo">The National</div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="faq-section">
        <div class="faq-container">
            <h2 class="section-title">Frequently Asked Questions</h2>
            <p class="section-description">We receive a lot of questions from our customers. You might also find answers to your questions here. Don't hesitate to contact us if you don't see your answer. Call us at: +971 *********</p>

            <div class="faq-item">
                <div class="faq-question">What is the purpose of obtaining a Schengen Visa?</div>
                <div class="faq-answer">The Schengen visa allows you to travel freely between 29 Schengen countries. The visa is stamped by the Embassy/Consulate where you will be travelling.</div>
            </div>

            <div class="faq-item">
                <div class="faq-question">Who needs a Schengen Visa?</div>
                <div class="faq-answer">Although certain groups and nationalities of countries are privileged to enter the Schengen visa-free zone, some others must meet all the requirements and attend interviews to get a visa that permits them to enter the Schengen Area.</div>
            </div>

            <div class="faq-item">
                <div class="faq-question">What's the role of White Wings in the visa assistance service?</div>
                <div class="faq-answer">Well, we, White Wings, offer visa assistance in Dubai, UAE, provides proper consultation and appointment bookings in VFS or respective consulates/ embassies, and also we assist you in document verification and preparation of necessary documents if required.</div>
            </div>

            <div class="faq-item">
                <div class="faq-question">Is there a guarantee for the Visa?</div>
                <div class="faq-answer">Please note that while we provide comprehensive visa assistance services, we cannot guarantee visa approval. Our team is dedicated to ensuring a smooth and efficient visa application process, but the final decision rests with the respective authorities.</div>
            </div>

            <div class="faq-item">
                <div class="faq-question">How long does it take to get a visa?</div>
                <div class="faq-answer">The duration of the visa application process can vary significantly depending on several factors, including the type of visa you are applying for and the country you are applying to. The visa application process takes almost 2 weeks to 4 months, based on your visa type.</div>
            </div>
        </div>
    </section>

    <!-- Contact Form Section -->
    <section class="contact-form-section" id="contact">
        <div class="contact-form-container">
            <div class="contact-info">
                <h2>Want to know more?</h2>
                <p>Fill out this form. We will connect you back soon!</p>
                <p><strong>Or connect us through any of our social media channel!</strong></p>

                <div class="social-buttons">
                    <a href="https://bit.ly/hellowhitewingsae" class="social-btn whatsapp">Whatsapp</a>
                    <a href="https://www.instagram.com/whitewings/" class="social-btn instagram">Instagram</a>
                    <a href="https://www.facebook.com/people/White-Wings/" class="social-btn facebook">Facebook</a>
                    <a href="https://www.linkedin.com/company/white-wings/" class="social-btn linkedin">Linkedin</a>
                    <a href="https://twitter.com/WhiteWings_" class="social-btn twitter">Twitter</a>
                    <a href="https://www.snapchat.com/add/whitewings.ae" class="social-btn snapchat">Snapchat</a>
                    <a href="https://www.youtube.com/@whitewings" class="social-btn youtube">Youtube</a>
                </div>

                <div class="service-buttons">
                    <a href="https://wa.link/ikcm1z" class="btn-primary">💬 WhatsApp</a>
                    <a href="tel:+971585945676" class="btn-secondary">📞 Call Us</a>
                </div>
            </div>

            <div class="contact-form">
                <h3 style="color: #00bfff; margin-bottom: 20px;">Contact Form</h3>
                <p style="color: #666; margin-bottom: 30px;">Fill out all fields below to get started</p>

                <form>
                    <div class="form-group">
                        <label>Your name *</label>
                        <input type="text" placeholder="Enter your full name" required>
                    </div>

                    <div class="form-group">
                        <label>Your email *</label>
                        <input type="email" placeholder="Enter your email address" required>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label>UAE Phone Number *</label>
                            <input type="tel" placeholder="+971 xxx xxx xxx" required>
                        </div>
                        <div class="form-group">
                            <label>Whatsapp *</label>
                            <input type="tel" placeholder="WhatsApp number" required>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label>Travelling to *</label>
                            <select required>
                                <option value="">Select destination</option>
                                <option value="usa">USA</option>
                                <option value="uk">UK</option>
                                <option value="canada">Canada</option>
                                <option value="schengen">Schengen</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Nationality *</label>
                            <input type="text" placeholder="Your nationality" required>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label>Profession on UAE residency visa *</label>
                            <input type="text" placeholder="Your profession" required>
                        </div>
                        <div class="form-group">
                            <label>Your salary range *</label>
                            <select required>
                                <option value="">Select salary range</option>
                                <option value="above8000">Above 8000 AED</option>
                                <option value="below8000">Below 8000 AED</option>
                            </select>
                        </div>
                    </div>

                    <button type="submit" class="submit-btn">Submit Application</button>
                </form>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section">
        <div class="cta-container">
            <h2>APPLY FOR A VISA</h2>
            <p>You do the flying, we'll handle the faff.</p>
            <p>Visa application processes are time consuming, complex and require a detailed approach. We can simplify the whole process for you – we'll handle the paperwork. Your vacation shouldn't give you another day 'at office'.</p>
            <a href="#contact" class="cta-btn">APPLY NOW</a>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-container">
            <div class="footer-grid">
                <div class="footer-brand">
                    <h3>White Wings</h3>
                    <div class="brand-tagline">Reach New Heights</div>
                    <p>Hassle-free travel shouldn't just be a dream.</p>
                    <p>Professional visa assistance services in Dubai, UAE. We specialize in making visa applications simple and stress-free for all destinations worldwide.</p>
                </div>

                <div class="footer-links">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="#">About</a></li>
                        <li><a href="#">Service</a></li>
                        <li><a href="#">Blog</a></li>
                        <li><a href="#">Contact</a></li>
                        <li><a href="#">Privacy Policy</a></li>
                        <li><a href="#">Terms And Condition</a></li>
                    </ul>
                </div>

                <div class="footer-contact">
                    <h4>Get In Touch</h4>
                    <p><strong>📞 Phone Numbers:</strong></p>
                    <p>+971 525 263 232<br>+971 585 945 989</p>
                    <p><strong>✉️ Email:</strong></p>
                    <p><strong><EMAIL></strong></p>
                    <p><strong>🏢 Office Hours:</strong></p>
                    <p>Sunday - Thursday: 9:00 AM - 6:00 PM<br>Saturday: 10:00 AM - 4:00 PM</p>
                </div>
            </div>

            <div class="footer-bottom">
                <p>Copyright © 2025 White Wings. All Rights Reserved.</p>
                <p>Powered by White Wings</p>
            </div>
        </div>
    </footer>
</body>
</html>
